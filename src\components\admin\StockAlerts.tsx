import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  AlertTriangle,
  Bell,
  BellOff,
  Check,
  Search,
  Clock,
  Package
} from 'lucide-react';
import { useProducts } from '@/contexts/ProductContext';
import { inventoryAnalyticsService, StockAlert } from '@/services/inventoryAnalyticsService';
import { restockInventory } from '@/services/inventoryService';
import { logger } from '@/utils/logger';
import { toast } from 'sonner';

interface StockAlertsProps {
  className?: string;
}

const StockAlerts: React.FC<StockAlertsProps> = ({ className = '' }) => {
  const { products } = useProducts();
  const [alerts, setAlerts] = useState<StockAlert[]>([]);
  const [filteredAlerts, setFilteredAlerts] = useState<StockAlert[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [showAcknowledged, setShowAcknowledged] = useState(false);

  // Load alerts
  useEffect(() => {
    loadAlerts();
  }, [products]);

  // Filter alerts based on search and filters
  useEffect(() => {
    let filtered = alerts;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(alert =>
        alert.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alert.message.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Severity filter
    if (severityFilter !== 'all') {
      filtered = filtered.filter(alert => alert.severity === severityFilter);
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(alert => alert.alertType === typeFilter);
    }

    // Acknowledged filter
    if (!showAcknowledged) {
      filtered = filtered.filter(alert => !alert.acknowledged);
    }

    setFilteredAlerts(filtered);
  }, [alerts, searchTerm, severityFilter, typeFilter, showAcknowledged]);

  const loadAlerts = async () => {
    setLoading(true);
    try {
      const generatedAlerts = inventoryAnalyticsService.generateStockAlerts(products);
      const existingAlerts = inventoryAnalyticsService.getActiveAlerts();
      
      // Combine and deduplicate alerts
      const allAlerts = [...existingAlerts, ...generatedAlerts];
      const uniqueAlerts = allAlerts.filter((alert, index, self) =>
        index === self.findIndex(a => a.productId === alert.productId && a.alertType === alert.alertType)
      );

      setAlerts(uniqueAlerts);
      
      logger.userAction('stock_alerts_loaded', {
        totalAlerts: uniqueAlerts.length,
        activeAlerts: uniqueAlerts.filter(a => !a.acknowledged).length
      });
    } catch (error) {
      logger.error('Failed to load stock alerts', error);
      toast.error('Failed to load stock alerts');
    } finally {
      setLoading(false);
    }
  };

  const handleAcknowledgeAlert = async (alertId: string) => {
    try {
      const success = inventoryAnalyticsService.acknowledgeAlert(alertId);
      if (success) {
        setAlerts(prev => prev.map(alert =>
          alert.id === alertId ? { ...alert, acknowledged: true } : alert
        ));
        toast.success('Alert acknowledged');
        
        logger.userAction('alert_acknowledged', { alertId });
      } else {
        toast.error('Failed to acknowledge alert');
      }
    } catch (error) {
      logger.error('Failed to acknowledge alert', error);
      toast.error('Failed to acknowledge alert');
    }
  };

  const handleQuickRestock = async (productId: string, alertId: string) => {
    try {
      const result = restockInventory(productId, 25); // Default restock quantity
      if (result) {
        await handleAcknowledgeAlert(alertId);
        await loadAlerts();
        toast.success('Product restocked and alert resolved');
        
        logger.userAction('quick_restock_from_alert', {
          productId,
          alertId,
          quantity: 25
        });
      } else {
        toast.error('Failed to restock product');
      }
    } catch (error) {
      logger.error('Failed to restock product', error);
      toast.error('Failed to restock product');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500/20 text-red-500 border-red-500/30';
      case 'high':
        return 'bg-orange-500/20 text-orange-500 border-orange-500/30';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-500 border-yellow-500/30';
      case 'low':
        return 'bg-blue-500/20 text-blue-500 border-blue-500/30';
      default:
        return 'bg-gray-500/20 text-gray-500 border-gray-500/30';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      case 'medium':
        return <Bell className="h-5 w-5 text-yellow-500" />;
      case 'low':
        return <Bell className="h-5 w-5 text-blue-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'out_of_stock':
        return 'Out of Stock';
      case 'low_stock':
        return 'Low Stock';
      case 'reorder_point':
        return 'Reorder Point';
      case 'overstock':
        return 'Overstock';
      default:
        return type;
    }
  };

  if (loading) {
    return (
      <Card className={`p-6 bg-sabone-dark-olive/60 border-sabone-gold/20 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-sabone-gold/20 rounded w-1/3"></div>
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-16 bg-sabone-gold/10 rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-6 bg-sabone-dark-olive/60 border-sabone-gold/20 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <AlertTriangle className="h-6 w-6 text-sabone-gold mr-2" />
          <h3 className="text-xl font-playfair font-semibold text-sabone-gold">
            Stock Alerts ({filteredAlerts.filter(a => !a.acknowledged).length})
          </h3>
        </div>
        <Button
          onClick={loadAlerts}
          size="sm"
          variant="outline"
          className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
        >
          <Bell className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-sabone-cream/60" />
          <Input
            placeholder="Search alerts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-sabone-charcoal/30 border-sabone-gold/20 text-sabone-cream"
          />
        </div>

        <select
          value={severityFilter}
          onChange={(e) => setSeverityFilter(e.target.value)}
          className="bg-sabone-charcoal/30 border border-sabone-gold/20 text-sabone-cream rounded px-3 py-2"
        >
          <option value="all">All Severities</option>
          <option value="critical">Critical</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>

        <select
          value={typeFilter}
          onChange={(e) => setTypeFilter(e.target.value)}
          className="bg-sabone-charcoal/30 border border-sabone-gold/20 text-sabone-cream rounded px-3 py-2"
        >
          <option value="all">All Types</option>
          <option value="out_of_stock">Out of Stock</option>
          <option value="low_stock">Low Stock</option>
          <option value="reorder_point">Reorder Point</option>
          <option value="overstock">Overstock</option>
        </select>

        <Button
          onClick={() => setShowAcknowledged(!showAcknowledged)}
          variant="outline"
          size="sm"
          className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
        >
          {showAcknowledged ? <BellOff className="w-4 h-4 mr-2" /> : <Bell className="w-4 h-4 mr-2" />}
          {showAcknowledged ? 'Hide Acknowledged' : 'Show Acknowledged'}
        </Button>
      </div>

      <Separator className="bg-sabone-gold/20 mb-6" />

      {/* Alerts List */}
      <div className="space-y-4">
        {filteredAlerts.length === 0 ? (
          <div className="text-center py-8">
            <Package className="h-12 w-12 text-sabone-cream/40 mx-auto mb-4" />
            <p className="text-sabone-cream/60">
              {alerts.length === 0 ? 'No alerts found' : 'No alerts match your filters'}
            </p>
          </div>
        ) : (
          filteredAlerts.map((alert) => (
            <div
              key={alert.id}
              className={`p-4 rounded-lg border transition-all duration-200 ${
                alert.acknowledged
                  ? 'bg-sabone-charcoal/20 border-sabone-cream/10 opacity-60'
                  : 'bg-sabone-charcoal/30 border-sabone-gold/20 hover:border-sabone-gold/40'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  {getSeverityIcon(alert.severity)}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-medium text-sabone-cream">{alert.productName}</h4>
                      <Badge variant="outline" className={getSeverityColor(alert.severity)}>
                        {alert.severity}
                      </Badge>
                      <Badge variant="outline" className="bg-sabone-gold/20 text-sabone-gold border-sabone-gold/30">
                        {getTypeLabel(alert.alertType)}
                      </Badge>
                      {alert.acknowledged && (
                        <Badge variant="outline" className="bg-green-500/20 text-green-500 border-green-500/30">
                          Acknowledged
                        </Badge>
                      )}
                    </div>
                    <p className="text-sabone-cream/80 mb-2">{alert.message}</p>
                    <div className="flex items-center text-sm text-sabone-cream/60">
                      <Clock className="h-4 w-4 mr-1" />
                      {new Date(alert.createdAt).toLocaleString()}
                    </div>
                  </div>
                </div>

                {!alert.acknowledged && (
                  <div className="flex items-center space-x-2">
                    {(alert.alertType === 'out_of_stock' || alert.alertType === 'low_stock') && (
                      <Button
                        onClick={() => handleQuickRestock(alert.productId, alert.id)}
                        size="sm"
                        className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal"
                      >
                        <Package className="w-4 h-4 mr-1" />
                        Restock
                      </Button>
                    )}
                    <Button
                      onClick={() => handleAcknowledgeAlert(alert.id)}
                      size="sm"
                      variant="outline"
                      className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                    >
                      <Check className="w-4 h-4 mr-1" />
                      Acknowledge
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary */}
      {filteredAlerts.length > 0 && (
        <div className="mt-6 pt-4 border-t border-sabone-gold/20">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-red-500">
                {filteredAlerts.filter(a => a.severity === 'critical' && !a.acknowledged).length}
              </div>
              <div className="text-sm text-sabone-cream/60">Critical</div>
            </div>
            <div>
              <div className="text-lg font-bold text-orange-500">
                {filteredAlerts.filter(a => a.severity === 'high' && !a.acknowledged).length}
              </div>
              <div className="text-sm text-sabone-cream/60">High</div>
            </div>
            <div>
              <div className="text-lg font-bold text-yellow-500">
                {filteredAlerts.filter(a => a.severity === 'medium' && !a.acknowledged).length}
              </div>
              <div className="text-sm text-sabone-cream/60">Medium</div>
            </div>
            <div>
              <div className="text-lg font-bold text-blue-500">
                {filteredAlerts.filter(a => a.severity === 'low' && !a.acknowledged).length}
              </div>
              <div className="text-sm text-sabone-cream/60">Low</div>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default StockAlerts;
