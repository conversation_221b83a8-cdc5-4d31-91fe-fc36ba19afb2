import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { LogIn } from "lucide-react";

// Form schema for validation
const signInFormSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
});

type SignInFormValues = z.infer<typeof signInFormSchema>;

interface SignInModalProps {
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSignUpClick?: () => void;
}

const SignInModal = ({ trigger, open, onOpenChange, onSignUpClick }: SignInModalProps) => {
  const { login } = useAuth();

  // Use the form hook for validation
  const form = useForm<SignInFormValues>({
    resolver: zodResolver(signInFormSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Handle dialog open state changes
  const handleOpenChange = (newOpenState: boolean) => {
    console.log('SignInModal: handleOpenChange called with', newOpenState);
    if (onOpenChange) {
      onOpenChange(newOpenState);
    }
  };

  const onSubmit = (data: SignInFormValues) => {
    // Since we're using Auth0, we'll just call the login function
    // which will redirect to Auth0's login page
    login();
  };

  const handleGoogleSignIn = () => {
    // Auth0 will handle the OAuth flow
    login();
  };

  // Log when the component renders
  console.log('SignInModal rendering, open prop:', open);

  return (
    <Dialog
      open={open}
      onOpenChange={handleOpenChange}
      modal={true}
    >
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="sm:max-w-md bg-sabone-dark-olive/95 backdrop-blur-md border-sabone-gold/30 text-sabone-cream animate-fade-in">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-2xl md:text-3xl text-sabone-gold font-playfair text-center">
            Welcome Back
          </DialogTitle>
          <DialogDescription className="text-sabone-cream/90 text-center">
            Sign in to your account to access your profile, orders, and more.
          </DialogDescription>
        </DialogHeader>

        <div className="relative my-2">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-sabone-gold/20"></span>
          </div>
          <div className="relative flex justify-center text-xs">
            <span className="bg-sabone-dark-olive/95 px-2 text-sabone-gold-light">
              Sign In Options
            </span>
          </div>
        </div>

        <div className="flex flex-col space-y-4">
          <Button
            variant="outline"
            onClick={handleGoogleSignIn}
            className="bg-sabone-charcoal-deep border-sabone-gold/30 text-sabone-cream hover:bg-sabone-gold/10 hover:border-sabone-gold/50 transition-all duration-300"
          >
            <svg className="mr-2 h-4 w-4" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="google" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 488 512">
              <path fill="currentColor" d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"></path>
            </svg>
            Continue with Google
          </Button>

          <div className="relative my-2">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-sabone-gold/20"></span>
            </div>
            <div className="relative flex justify-center text-xs">
              <span className="bg-sabone-dark-olive/95 px-2 text-sabone-gold-light">
                Or with email
              </span>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sabone-cream">Email</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="<EMAIL>"
                        className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream focus-visible:ring-sabone-gold/50 focus-visible:border-sabone-gold/50"
                      />
                    </FormControl>
                    <FormMessage className="text-red-400" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sabone-cream">Password</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="password"
                        placeholder="••••••••"
                        className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream focus-visible:ring-sabone-gold/50 focus-visible:border-sabone-gold/50"
                      />
                    </FormControl>
                    <FormMessage className="text-red-400" />
                  </FormItem>
                )}
              />
              <Button
                type="submit"
                className="w-full bg-sabone-gold hover:bg-sabone-gold-rich text-sabone-charcoal font-medium transition-all duration-300"
              >
                <LogIn className="mr-2 h-4 w-4" />
                Sign In
              </Button>
            </form>
          </Form>
        </div>

        <div className="mt-4 text-center text-sm text-sabone-cream/70">
          <p>
            Don't have an account?{" "}
            <DialogClose asChild>
              <Button
                variant="link"
                className="p-0 text-sabone-gold hover:text-sabone-gold-accent"
                onClick={onSignUpClick}
              >
                Sign up
              </Button>
            </DialogClose>
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SignInModal;
